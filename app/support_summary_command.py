"""
Support Summary Command Handler Module

This module handles the /support-summary Slack command for generating
summaries of recently modified Jira support tickets.
"""

import logging
import traceback
from datetime import datetime, timedelta
from typing import Tuple, List, Dict, Any
import re

from .jira_client import JiraClient

logger = logging.getLogger(__name__)


CUSTOM_FIELD_LOOKUP = {
    "implements": "customfield_10255" # List of dicts where "value" is the implement number formatted as "R123"
}


def format_summary_for_display(summary: str, max_length: int = 60) -> str:
    """
    Format issue summary for display in the table.
    
    Args:
        summary: The original summary
        max_length: Maximum length for display
        
    Returns:
        str: Formatted summary
    """
    # Remove robot identifier from the beginning if present
    robot_pattern = r'^[SR]\d+\s*[-:]?\s*'
    summary = re.sub(robot_pattern, '', summary, flags=re.IGNORECASE)
    
    # Truncate if too long
    if len(summary) > max_length:
        summary = summary[:max_length-3] + "..."
    
    return summary


def format_status(status_name: str) -> str:
    """
    Format status for display.
    
    Args:
        status_name: The Jira status name
        
    Returns:
        str: Formatted status
    """
    # Map common Jira statuses to display names
    status_mapping = {
        'done': 'Done',
        'closed': 'Done',
        'resolved': 'Done',
        'in progress': 'In Progress',
        'in review': 'In Review',
        'to do': 'To Do',
        'open': 'Open',
        'new': 'New',
    }
    
    return status_mapping.get(status_name.lower(), status_name)


def format_support_summary(issues: List[Dict[str, Any]], title: str = "Support Summary") -> str:
    """
    Format the issues into a nice table for Slack display.

    Args:
        issues: List of Jira issue dictionaries
        title: Title for the summary

    Returns:
        str: Formatted summary table
    """
    if not issues:
        return f"{title}:\nNo issues found."

    # First loop: gather all display information
    issue_data = []
    for issue in issues:
        try:
            fields = issue.get('fields', {})
            key = issue.get('key', 'Unknown')
            summary = fields.get('summary', 'No summary')
            status = fields.get('status', {}).get('name', 'Unknown')

            # Extract robot identifier
            implements = fields.get(CUSTOM_FIELD_LOOKUP["implements"])
            
            if len(implements) == 0:
                implement = "???"
            else:
                implement = implements[0]["value"]
            
            logger.info(f"fields {fields}")

            # Format summary for display
            summary_display = format_summary_for_display(summary)

            # Format status
            status_display = format_status(status)

            issue_data.append({
                'robot_display': implement,
                'summary_display': summary_display,
                'status_display': status_display,
                'key': key
            })

        except Exception as e:
            logger.error(f"Error formatting issue {issue.get('key', 'Unknown')}: {e}")
            traceback.print_exc()
            continue

    if not issue_data:
        return f"{title}:\nNo valid issues found."

    # Use a much simpler approach: just use enough tabs to create reasonable spacing
    # This should work better in Slack's rendering
    lines = [f"{title}:"]

    for item in issue_data:
        # Use a simple approach: robot gets 2 tabs, summary gets enough tabs based on length, status gets 1 tab
        robot_part = item['robot_display'] + '\t\t'

        # For summary, use more tabs if it's short, fewer if it's long
        summary_len = len(item['summary_display'])
        if summary_len < 20:
            summary_tabs = '\t\t\t\t'
        elif summary_len < 40:
            summary_tabs = '\t\t\t'
        elif summary_len < 60:
            summary_tabs = '\t\t'
        else:
            summary_tabs = '\t'

        summary_part = item['summary_display'] + summary_tabs
        status_part = item['status_display'] + '\t'

        line = f"{robot_part}{summary_part}{status_part}{item['key']}"
        lines.append(line)
        
    

    return "\n".join(lines)


def get_time_period_from_command(command_text: str) -> Tuple[datetime, str]:
    """
    Parse the command text to determine the time period to search.
    
    Args:
        command_text: The text following the /support-summary command
        
    Returns:
        tuple: (since_datetime, period_description)
    """
    now = datetime.now()
    
    if not command_text or command_text.strip() == "":
        # Default: last 24 hours
        since = now - timedelta(hours=24)
        return since, "Last 24 Hours"
    
    text = command_text.strip().lower()
    
    if "today" in text:
        # Since start of today
        since = now.replace(hour=0, minute=0, second=0, microsecond=0)
        return since, "Today"
    elif "yesterday" in text:
        # Yesterday only
        yesterday = now - timedelta(days=1)
        since = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        return since, "Yesterday"
    elif "week" in text:
        # Last 7 days
        since = now - timedelta(days=7)
        return since, "Last Week"
    elif "monday" in text or "evening" in text:
        # Since Monday evening (common support shift)
        days_since_monday = now.weekday()  # 0 = Monday
        monday = now - timedelta(days=days_since_monday)
        monday_evening = monday.replace(hour=17, minute=0, second=0, microsecond=0)
        return monday_evening, "Monday Evening"
    else:
        # Try to parse hours
        import re
        hours_match = re.search(r'(\d+)\s*h', text)
        if hours_match:
            hours = int(hours_match.group(1))
            since = now - timedelta(hours=hours)
            return since, f"Last {hours} Hours"
        
        # Default fallback
        since = now - timedelta(hours=24)
        return since, "Last 24 Hours"


def handle_support_summary_command(command_text: str = "") -> Tuple[bool, str]:
    """
    Handle the /support-summary command.
    
    Args:
        command_text: The text following the /support-summary command
        
    Returns:
        tuple: (success: bool, response_message: str)
    """
    try:
        since_datetime, period_description = get_time_period_from_command(command_text)
        
        logger.info(f"Generating support summary for period: {period_description} (since {since_datetime})")
        
        client = JiraClient()
        
        projects = ["CRS"]
        issues = client.get_issues_modified_since(since_datetime, projects)
        
        if issues is None:
            error_msg = "❌ Error: Failed to retrieve issues from Jira. Please check the configuration."
            return False, error_msg
        
        # Format the summary
        summary = format_support_summary(issues, f"{period_description} Support Summary")
        
        return True, summary
        
    except Exception as e:
        logger.error(f"Error handling /support-summary command: {e}")
        traceback.print_exc()
        error_msg = "❌ Error: Failed to generate support summary"
        return False, error_msg
